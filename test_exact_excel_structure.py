#!/usr/bin/env python3
"""
Test script that exactly simulates your Excel file structure to verify the fix works.
This creates a test Excel file with the exact column structure from your log.
"""

import pandas as pd
import numpy as np
import asyncio
from enhanced_categorization_tool import EnhancedWeightRulesProductCategorizationTool

def create_test_excel_file():
    """Create a test Excel file with your exact column structure"""
    print("🧪 Creating test Excel file with exact structure...")
    
    # Create test data matching your exact Excel structure
    test_data = {
        'id': ['HU_220368432', 'HU_220368433', 'HU_220368434', 'HU_220368435', 'HU_220368436'],
        'country': ['HU', 'HU', 'HU', 'HU', 'HU'],
        'category': ['Home', 'Home', 'Home', 'Home', 'Home'],
        'tpn': ['2004020368432', '2004020368433', '2004020368434', '2004020368435', '2004020368436'],
        'tpnb': ['220368432', '220368433', '220368434', '220368435', '220368436'],
        'product_name': [
            'HAMMERSMITH HYDR',
            'Test Product 250g',
            'Sample Item 500ml',
            'Demo Product 1kg',
            'Example Item 150g x 6'
        ],
        'order_type': ['PBL', 'PBL', 'PBL', 'PBL', 'PBL'],
        'unit_type': ['0', '0', '0', '0', '0'],
        'case_capacity': ['0', '8', '12', '6', '0'],  # Mixed with zeros and strings
        'weight': ['0', '1.2', '2.5', '1.8', '0'],   # This is the problematic column
        'DIV_DESC': ['no data', 'Fresh Food', 'Grocery', 'Fresh Food', 'no data'],
        'DIV_ID': ['0', '1', '2', '1', '0'],
        'DEP_DESC': ['no data', 'Dairy', 'Beverages', 'Meat', 'no data'],
        'DEP_ID': ['0', '1', '2', '3', '0'],
        'SEC_DESC': ['no data', 'Milk', 'Soft Drinks', 'Fresh Meat', 'no data'],
        'SEC_ID': ['0', '1', '2', '3', '0'],
        'GRP_DESC': ['no data', 'Milk Products', 'Carbonated', 'Beef', 'no data'],
        'GRP_ID': ['0', '1', '2', '3', '0'],
        'SGR_DESC': ['no data', 'Whole Milk', 'Cola', 'Ground Beef', 'no data'],
        'SGR_ID': ['0', '1', '2', '3', '0']
    }
    
    df = pd.DataFrame(test_data)
    
    # Save as Excel file
    test_file = 'test_exact_structure.xlsx'
    df.to_excel(test_file, index=False)
    
    print(f"✅ Created test Excel file: {test_file}")
    print("Sample data:")
    print(df[['product_name', 'case_capacity', 'weight', 'DIV_DESC', 'DEP_DESC']].head())
    print(f"Data types:\n{df.dtypes}")
    
    return test_file, df

def create_training_file():
    """Create a minimal training file for testing"""
    print("\n🧪 Creating training file...")
    
    training_data = {
        'product_name': [
            'Training Product 1',
            'Training Product 2', 
            'Training Product 3',
            'Training Product 4',
            'Training Product 5'
        ],
        'case_capacity': [8, 12, 6, 10, 4],
        'case_weight': [2.0, 3.0, 1.5, 2.5, 1.0],
        'DIV_DESC': ['Fresh Food', 'Grocery', 'Fresh Food', 'Grocery', 'Fresh Food'],
        'DEP_DESC': ['Dairy', 'Beverages', 'Meat', 'Beverages', 'Dairy'],
        'SEC_DESC': ['Milk', 'Soft Drinks', 'Fresh Meat', 'Soft Drinks', 'Milk'],
        'GRP_DESC': ['Milk Products', 'Carbonated', 'Beef', 'Carbonated', 'Milk Products'],
        'PBL category': ['Deli and dairy 0-4', 'Light Laundry & Cleaning 0-4', 'Deli and dairy 0-4', 'Light Laundry & Cleaning 0-4', 'Deli and dairy 0-4']
    }
    
    training_df = pd.DataFrame(training_data)
    training_file = 'test_training_exact.xlsx'
    training_df.to_excel(training_file, index=False)
    
    print(f"✅ Created training file: {training_file}")
    return training_file

async def test_exact_processing():
    """Test processing with the exact Excel structure"""
    print("\n🧪 Testing processing with exact Excel structure...")
    
    # Create test files
    test_file, original_df = create_test_excel_file()
    training_file = create_training_file()
    
    # Load the Excel file (simulating how the app loads it)
    loaded_df = pd.read_excel(test_file)
    print(f"\nLoaded DataFrame columns: {list(loaded_df.columns)}")
    print(f"Loaded DataFrame data types:\n{loaded_df.dtypes}")
    
    # Initialize the tool
    tool = EnhancedWeightRulesProductCategorizationTool()
    
    print(f"\n🔄 Processing with enhanced tool...")
    
    try:
        # This should work without the 'case_weight' error
        processed_df = await tool.process_products_with_zero_prediction(
            loaded_df, 
            training_file, 
            'PBL category'
        )
        
        print("✅ Processing completed successfully!")
        
        # Show results
        print("\nProcessed data sample:")
        result_cols = ['product_name', 'case_capacity', 'case_weight']
        if 'predicted_case_capacity' in processed_df.columns:
            result_cols.append('predicted_case_capacity')
        if 'predicted_case_weight' in processed_df.columns:
            result_cols.append('predicted_case_weight')
        if 'predicted_pbl_category' in processed_df.columns:
            result_cols.append('predicted_pbl_category')
            
        print(processed_df[result_cols].head())
        
        # Verify that the problematic comparisons worked
        capacity_zeros = (processed_df['case_capacity'] == 0).sum()
        weight_zeros = (processed_df['case_weight'] == 0).sum()
        print(f"\nZero values found and processed:")
        print(f"  Case capacity zeros: {capacity_zeros}")
        print(f"  Case weight zeros: {weight_zeros}")
        
        return True
        
    except KeyError as e:
        if 'case_weight' in str(e):
            print(f"❌ Column mapping failed - still getting case_weight error: {e}")
            return False
        else:
            print(f"❌ Different KeyError: {e}")
            return False
    except Exception as e:
        print(f"❌ Processing failed with error: {e}")
        print(f"Error type: {type(e).__name__}")
        return False

def test_column_detection():
    """Test that the column mapping is detected correctly"""
    print("\n🧪 Testing column detection and mapping...")
    
    # Create DataFrame with your exact structure
    test_data = {
        'product_name': ['HAMMERSMITH HYDR', 'Test Product'],
        'case_capacity': ['0', '8'],
        'weight': ['0', '1.2'],  # This should be detected and mapped
        'DIV_DESC': ['no data', 'Fresh Food'],
        'DEP_DESC': ['no data', 'Dairy']
    }
    
    df = pd.DataFrame(test_data)
    print("Original DataFrame:")
    print(df)
    print(f"Original columns: {list(df.columns)}")
    
    # Test column mapping
    tool = EnhancedWeightRulesProductCategorizationTool()
    
    # Test mapping
    mapped_df = tool._map_column_names(df)
    print(f"\nAfter mapping columns: {list(mapped_df.columns)}")
    
    # Test preprocessing
    processed_df = tool._preprocess_numeric_columns(df)
    print(f"After preprocessing columns: {list(processed_df.columns)}")
    print(f"Processed data types:\n{processed_df.dtypes}")
    
    # Verify results
    assert 'case_weight' in processed_df.columns, "Should have case_weight after mapping"
    assert processed_df['case_weight'].dtype in ['float64', 'int64'], "case_weight should be numeric"
    
    print("✅ Column detection and mapping test passed!")

async def main():
    """Run all tests"""
    print("🚀 Starting exact Excel structure tests...\n")
    
    try:
        # Test 1: Column detection
        test_column_detection()
        
        # Test 2: Full processing with exact structure
        success = await test_exact_processing()
        
        if success:
            print("\n🎉 All tests passed!")
            print("✅ Your Excel file structure is now fully supported!")
            print("✅ The 'weight' column will be automatically mapped to 'case_weight'")
            print("✅ The type comparison errors have been resolved!")
            print("✅ The application should now process your 3531 records successfully!")
        else:
            print("\n❌ Some tests failed!")
            
    except Exception as e:
        print(f"\n❌ Tests failed with error: {e}")
        raise
    finally:
        # Clean up test files
        import os
        for file in ['test_exact_structure.xlsx', 'test_training_exact.xlsx']:
            if os.path.exists(file):
                os.remove(file)
                print(f"🧹 Cleaned up {file}")

if __name__ == "__main__":
    asyncio.run(main())
