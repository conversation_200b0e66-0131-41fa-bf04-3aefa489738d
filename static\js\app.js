// Enhanced JavaScript functionality for WLM Category Classifier

document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    setupFileDropZone();
    setupAnimations();
    setupTooltips();
    setupProgressTracking();
}

// File drop zone functionality
function setupFileDropZone() {
    const dropZone = document.querySelector('.border-dashed');
    const fileInput = document.getElementById('file-input');
    
    if (!dropZone || !fileInput) return;
    
    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });
    
    // Highlight drop zone when item is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
        dropZone.addEventListener(eventName, () => {
            dropZone.classList.add('dragover');
            dropZone.classList.add('border-blue-500');
            dropZone.classList.remove('border-blue-300');
        }, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, () => {
            dropZone.classList.remove('dragover');
            dropZone.classList.remove('border-blue-500');
            dropZone.classList.add('border-blue-300');
        }, false);
    });
    
    // Handle dropped files
    dropZone.addEventListener('drop', handleDrop, false);
}

function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
}

function handleDrop(e) {
    const dt = e.dataTransfer;
    const files = dt.files;
    
    if (files.length > 0) {
        const file = files[0];
        if (file.type.includes('sheet') || file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
            document.getElementById('file-input').files = files;
            updateFileName(document.getElementById('file-input'));
            
            // Add visual feedback
            showNotification('File ready for processing!', 'success');
        } else {
            showNotification('Please select an Excel file (.xlsx or .xls)', 'error');
        }
    }
}

// Animation setup
function setupAnimations() {
    // Animate elements when they come into view
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe all cards and sections
    document.querySelectorAll('.bg-white, .card-hover').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'all 0.6s ease';
        observer.observe(el);
    });
}

// Tooltip functionality
function setupTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(e) {
    const tooltipText = e.target.getAttribute('data-tooltip');
    const tooltip = document.createElement('div');
    
    tooltip.className = 'absolute z-50 bg-gray-900 text-white text-sm rounded py-2 px-3 shadow-lg';
    tooltip.textContent = tooltipText;
    tooltip.id = 'tooltip';
    
    document.body.appendChild(tooltip);
    
    const rect = e.target.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
}

function hideTooltip() {
    const tooltip = document.getElementById('tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

// Progress tracking functionality
function setupProgressTracking() {
    // Enhance progress bar with smoother animations
    const progressBar = document.getElementById('progress-bar');
    if (progressBar) {
        progressBar.style.transition = 'width 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
    }
}

// Notification system
function showNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-x-full`;
    
    const icons = {
        success: 'fas fa-check-circle text-green-600',
        error: 'fas fa-exclamation-circle text-red-600',
        info: 'fas fa-info-circle text-blue-600',
        warning: 'fas fa-exclamation-triangle text-yellow-600'
    };
    
    const colors = {
        success: 'bg-green-50 border-green-200 text-green-800',
        error: 'bg-red-50 border-red-200 text-red-800',
        info: 'bg-blue-50 border-blue-200 text-blue-800',
        warning: 'bg-yellow-50 border-yellow-200 text-yellow-800'
    };
    
    notification.innerHTML = `
        <div class="flex items-center ${colors[type]} border rounded-lg p-3">
            <i class="${icons[type]} mr-3"></i>
            <span class="font-medium">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Auto remove
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => notification.remove(), 300);
    }, duration);
}

// Enhanced progress updating with smooth transitions
function updateProgress(progress, message) {
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    const processingMessage = document.getElementById('processing-message');
    
    if (progressBar) {
        // Smooth progress bar animation
        progressBar.style.width = progress + '%';
        
        // Add pulsing effect during processing
        if (progress > 0 && progress < 100) {
            progressBar.classList.add('animate-pulse');
        } else {
            progressBar.classList.remove('animate-pulse');
        }
        
        // Change color based on progress
        if (progress >= 100) {
            progressBar.className = progressBar.className.replace(/from-\w+-\d+|to-\w+-\d+/g, '');
            progressBar.classList.add('from-green-600', 'to-emerald-600');
        }
    }
    
    if (progressText) {
        // Animate number changes
        const currentProgress = parseInt(progressText.textContent) || 0;
        animateNumber(progressText, currentProgress, progress, 500);
    }
    
    if (processingMessage && message) {
        // Fade between messages
        processingMessage.style.opacity = '0.5';
        setTimeout(() => {
            processingMessage.textContent = message;
            processingMessage.style.opacity = '1';
        }, 150);
    }
}

// Animate number changes
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    const difference = end - start;
    
    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // Easing function for smooth animation
        const easeOutCubic = 1 - Math.pow(1 - progress, 3);
        const current = Math.round(start + (difference * easeOutCubic));
        
        element.textContent = current + '%';
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    requestAnimationFrame(updateNumber);
}

// File size formatter
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Validate file before upload
function validateFile(file) {
    const maxSize = 100 * 1024 * 1024; // 100MB
    const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
    ];
    
    if (file.size > maxSize) {
        showNotification('File size exceeds 100MB limit', 'error');
        return false;
    }
    
    if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
        showNotification('Please select a valid Excel file', 'error');
        return false;
    }
    
    return true;
}

// Enhanced file input change handler
function updateFileName(input) {
    const file = input.files[0];
    if (!file) return;
    
    if (!validateFile(file)) {
        input.value = '';
        return;
    }
    
    const dropText = document.getElementById('drop-text');
    const fileSize = formatFileSize(file.size);
    
    dropText.innerHTML = `
        <i class="fas fa-file-excel text-4xl text-green-500 mb-3"></i>
        <p class="text-lg font-medium text-green-700">${file.name}</p>
        <p class="text-sm text-green-600 mt-1">${fileSize} • Ready to process</p>
    `;
    
    // Add success animation
    dropText.parentElement.classList.add('border-green-400', 'bg-green-50');
    dropText.parentElement.classList.remove('border-blue-300', 'bg-blue-50');
    
    // Only show notification if function exists
    if (typeof showNotification === 'function') {
        showNotification(`File "${file.name}" is ready for processing`, 'success');
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // ESC to close modals
    if (e.key === 'Escape') {
        const modal = document.getElementById('processing-modal');
        if (modal && !modal.classList.contains('hidden')) {
            // Don't close if processing is in progress
            return;
        }
    }
    
    // Ctrl+U or Cmd+U to focus file input
    if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
        e.preventDefault();
        const fileInput = document.getElementById('file-input');
        if (fileInput) {
            fileInput.click();
        }
    }
});

// Copy results to clipboard functionality
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('Copied to clipboard!', 'success');
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('Copied to clipboard!', 'success');
    }
}

// Print results functionality
function printResults() {
    const printContent = document.querySelector('.max-w-6xl').innerHTML;
    const printWindow = window.open('', '_blank');
    
    printWindow.document.write(`
        <html>
            <head>
                <title>WLM Category Classifier Results</title>
                <script src="https://cdn.tailwindcss.com"></script>
                <style>
                    @media print {
                        body { font-size: 12px; }
                        .no-print { display: none !important; }
                    }
                </style>
            </head>
            <body class="p-4">
                <div class="max-w-6xl mx-auto">
                    ${printContent}
                </div>
            </body>
        </html>
    `);
    
    printWindow.document.close();
    setTimeout(() => {
        printWindow.print();
        printWindow.close();
    }, 250);
}