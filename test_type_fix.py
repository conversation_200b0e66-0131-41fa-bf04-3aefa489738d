#!/usr/bin/env python3
"""
Test script to verify the type comparison fix for the WLM category classifier.
This script tests the data type handling improvements in the ZeroValuePredictor class.
"""

import pandas as pd
import numpy as np
from enhanced_categorization_tool import ZeroValuePredictor, EnhancedWeightRulesProductCategorizationTool

def test_safe_numeric_conversion():
    """Test the safe numeric conversion function"""
    print("🧪 Testing safe numeric conversion...")
    
    predictor = ZeroValuePredictor()
    
    # Test cases with different data types
    test_cases = [
        ("123", 123.0),
        ("123.45", 123.45),
        ("123,45", 123.45),  # European decimal format
        ("0", 0.0),
        ("", 0.0),  # Empty string should return default
        ("nan", 0.0),  # String 'nan' should return default
        ("null", 0.0),  # String 'null' should return default
        (123, 123.0),  # Integer
        (123.45, 123.45),  # Float
        (np.nan, 0.0),  # NaN should return default
        (None, 0.0),  # None should return default
        ("invalid", 0.0),  # Invalid string should return default
    ]
    
    for input_val, expected in test_cases:
        result = predictor._safe_numeric_conversion(input_val, 0)
        print(f"  Input: {repr(input_val):>12} -> Output: {result:>6} (Expected: {expected})")
        assert result == expected, f"Failed for input {input_val}: got {result}, expected {expected}"
    
    print("✅ Safe numeric conversion tests passed!")

def test_numeric_mask_creation():
    """Test the numeric mask creation for comparisons"""
    print("\n🧪 Testing numeric mask creation...")
    
    predictor = ZeroValuePredictor()
    
    # Create test dataframe with mixed data types
    test_data = {
        'case_capacity': [1, "2", "3.5", "0", "", "nan", 5, np.nan, None, "invalid"],
        'case_weight': ["1.5", 2, "0", "3,5", "", np.nan, None, "invalid", 7, "8.2"]
    }
    df = pd.DataFrame(test_data)
    
    print("Test DataFrame:")
    print(df)
    print(f"Data types:\n{df.dtypes}")
    
    # Test different comparison operations
    print("\nTesting comparisons:")
    
    # Test case_capacity > 0
    mask_gt = predictor._create_numeric_mask(df, 'case_capacity', 0, '>')
    print(f"case_capacity > 0: {mask_gt.tolist()}")
    expected_gt = [True, True, True, False, False, False, True, False, False, False]
    assert mask_gt.tolist() == expected_gt, f"Expected {expected_gt}, got {mask_gt.tolist()}"
    
    # Test case_capacity == 0
    mask_eq = predictor._create_numeric_mask(df, 'case_capacity', 0, '==')
    print(f"case_capacity == 0: {mask_eq.tolist()}")
    expected_eq = [False, False, False, True, True, True, False, True, True, True]
    assert mask_eq.tolist() == expected_eq, f"Expected {expected_eq}, got {mask_eq.tolist()}"
    
    # Test case_weight > 0
    mask_weight_gt = predictor._create_numeric_mask(df, 'case_weight', 0, '>')
    print(f"case_weight > 0: {mask_weight_gt.tolist()}")
    expected_weight_gt = [True, True, False, True, False, False, False, False, True, True]
    assert mask_weight_gt.tolist() == expected_weight_gt, f"Expected {expected_weight_gt}, got {mask_weight_gt.tolist()}"
    
    print("✅ Numeric mask creation tests passed!")

def test_data_preprocessing():
    """Test the data preprocessing function"""
    print("\n🧪 Testing data preprocessing...")
    
    tool = EnhancedWeightRulesProductCategorizationTool()
    
    # Create test dataframe with mixed data types
    test_data = {
        'product_name': ['Product A', 'Product B', 'Product C'],
        'case_capacity': ["8", 12, "6.5"],
        'case_weight': ["1.2", "2,5", 3],
        'unit_weight': ["0.15", 0.25, "0,3"],
        'other_column': ['A', 'B', 'C']  # Non-numeric column should be unchanged
    }
    df = pd.DataFrame(test_data)
    
    print("Original DataFrame:")
    print(df)
    print(f"Original data types:\n{df.dtypes}")
    
    # Preprocess the data
    processed_df = tool._preprocess_numeric_columns(df)
    
    print("\nProcessed DataFrame:")
    print(processed_df)
    print(f"Processed data types:\n{processed_df.dtypes}")
    
    # Verify that numeric columns are properly converted
    assert processed_df['case_capacity'].tolist() == [8.0, 12.0, 6.5]
    assert processed_df['case_weight'].tolist() == [1.2, 2.5, 3.0]
    assert processed_df['unit_weight'].tolist() == [0.15, 0.25, 0.3]
    assert processed_df['other_column'].tolist() == ['A', 'B', 'C']  # Should be unchanged
    
    print("✅ Data preprocessing tests passed!")

def test_comparison_operations():
    """Test that comparison operations work after preprocessing"""
    print("\n🧪 Testing comparison operations after preprocessing...")
    
    predictor = ZeroValuePredictor()
    
    # Create test dataframe with string values that would cause the original error
    test_data = {
        'case_capacity': ["8", "0", "12", "", "nan"],
        'case_weight': ["1.2", "0", "2,5", "", "invalid"],
        'DIV_DESC': ['Div1', 'Div1', 'Div2', 'Div1', 'Div2'],
        'DEP_DESC': ['Dep1', 'Dep1', 'Dep2', 'Dep1', 'Dep2']
    }
    df = pd.DataFrame(test_data)
    
    print("Test DataFrame with string values:")
    print(df)
    
    # This should not raise the "'>' not supported between instances of 'str' and 'int'" error
    try:
        predictor._calculate_case_capacity_stats(df)
        predictor._calculate_case_weight_stats(df)
        print("✅ Comparison operations completed without errors!")
    except TypeError as e:
        if "'>' not supported between instances of 'str' and 'int'" in str(e):
            print(f"❌ Original error still occurs: {e}")
            raise
        else:
            print(f"❌ Unexpected error: {e}")
            raise
    
    print("✅ Comparison operations tests passed!")

def main():
    """Run all tests"""
    print("🚀 Starting type comparison fix tests...\n")
    
    try:
        test_safe_numeric_conversion()
        test_numeric_mask_creation()
        test_data_preprocessing()
        test_comparison_operations()
        
        print("\n🎉 All tests passed! The type comparison fix is working correctly.")
        print("The application should now handle string values in numeric columns without errors.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise

if __name__ == "__main__":
    main()
