# 🏷️ WLM Category Classifier

A modern, sleek web application for advanced product data analysis with ML-powered weight extraction, zero value prediction, and intelligent PBL categorization.

![Application Screenshot](https://img.shields.io/badge/FastAPI-009688?style=for-the-badge&logo=FastAPI&logoColor=white)
![HTMX](https://img.shields.io/badge/HTMX-3366CC?style=for-the-badge&logo=htmx&logoColor=white)
![TailwindCSS](https://img.shields.io/badge/Tailwind_CSS-38B2AC?style=for-the-badge&logo=tailwind-css&logoColor=white)

## ✨ Features

### 🔍 **Advanced Data Processing**
- **Weight Extraction**: Automatically extracts and corrects unit weights from product names using sophisticated regex patterns
- **ML Prediction**: Uses machine learning to predict missing case capacities and weights based on hierarchical categories
- **Smart Categorization**: Intelligent PBL category assignment using trained models with hierarchical database knowledge

### 🎨 **Modern UI/UX**
- Sleek, professional interface with animated components
- Drag-and-drop file upload with visual feedback
- Real-time progress tracking with smooth animations
- Responsive design that works on all devices
- Interactive data visualizations and charts

### 📊 **Comprehensive Results**
- Detailed processing statistics and insights
- Visual charts showing category distributions
- Column-by-column processing breakdown
- Download processed results as Excel files
- Print-friendly results pages

## 🚀 Quick Start

### Option 1: Automatic Setup (Recommended)
```bash
cd wlm_category_classifier
python3 run.py
```

The startup script will:
1. Check for required dependencies
2. Offer to install missing packages automatically
3. Launch the appropriate version (full or demo)

### Option 2: Manual Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Run the full application
python3 -m uvicorn app:app --host 0.0.0.0 --port 8000 --reload
```

### Option 3: Demo Mode (No Dependencies Required)
```bash
python3 app_demo.py
```

## 📋 Requirements

### 📁 File Requirements
- **Format**: Excel files (.xlsx, .xls)
- **Max Size**: 100MB
- **Required Columns**:
  - `product_name` (or similar: name, product, desc)
  - `case_capacity`
  - `case_weight` 
  - `unit_weight`

### 🐍 System Requirements
- Python 3.8 or higher
- Required Python packages (see `requirements.txt`)

## 🔧 Dependencies

```txt
# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
jinja2==3.1.2

# Data Processing & ML
pandas==2.1.3
numpy==1.25.2
scikit-learn==1.3.2
openpyxl==3.1.2
xlrd==2.0.1
```

## 📖 How It Works

### 1. **File Upload**
- Drag & drop Excel files or click to browse
- Automatic file validation and size checking
- Visual feedback for file selection

### 2. **Data Processing**
The application performs several sophisticated operations:

#### Weight Extraction & Correction
```python
# Extracts weights from product names like:
# "Organic Bananas 1.5kg" → 1500g
# "Milk Bottle 2L" → 2000ml
# "Chocolate Bar 100g Pack" → 100g
```

#### ML-Powered Predictions
- Predicts missing case capacities using hierarchical category data
- Uses Random Forest models trained on existing product database
- Fills zero values based on similar products in the same categories

#### PBL Categorization
- Assigns products to appropriate PBL categories
- Uses TF-IDF vectorization of product names
- Trained on hierarchical database with DIV/DEP/SEC/GRP structure

### 3. **Results & Download**
- Comprehensive statistics dashboard
- Interactive charts and visualizations
- Download enhanced Excel file with all new columns
- Print-friendly results page

## 📊 Output Columns

The processed file includes these enhanced columns:

| Column | Description |
|--------|-------------|
| `extracted_unit_weight` | Weight extracted from product name |
| `corrected_unit_weight_g` | Normalized weight in grams |
| `calculated_case_weight` | Computed case weight |
| `predicted_case_capacity` | ML-predicted capacity values |
| `predicted_case_weight` | ML-predicted weight values |
| `predicted_pbl_category` | Assigned PBL category |

## 🛠️ Architecture

```
wlm_category_classifier/
├── app.py                     # Main FastAPI application
├── app_demo.py               # Demo version (no ML deps)
├── run.py                    # Startup script
├── enhanced_categorization_tool.py  # ML processing core
├── analyze_and_fix_data.py   # Original processing logic
├── requirements.txt          # Python dependencies
├── templates/
│   ├── index.html           # Main upload interface
│   └── results.html         # Results display page
└── static/
    ├── css/style.css        # Custom animations & styles
    └── js/app.js            # Enhanced interactivity
```

### Technology Stack
- **Backend**: FastAPI (Python)
- **Frontend**: HTMX + TailwindCSS + Vanilla JS
- **ML**: scikit-learn, pandas, numpy
- **Data**: openpyxl for Excel processing
- **Templates**: Jinja2
- **Charts**: Chart.js

## 🚦 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/` | Main upload interface |
| `POST` | `/upload` | File upload endpoint |
| `GET` | `/status/{task_id}` | Processing status |
| `GET` | `/results/{task_id}` | Results display page |
| `GET` | `/download/{task_id}` | Download processed file |

## 🎨 UI Components

### Modern Design Elements
- **Gradient backgrounds** with smooth color transitions
- **Glass-morphism effects** for modern card designs
- **Smooth animations** for all interactions
- **Loading states** with progress indicators
- **Responsive layouts** for all screen sizes

### Interactive Features
- **Drag & drop** file upload with visual feedback
- **Real-time progress** tracking with animated bars
- **Interactive charts** with hover effects
- **Copy to clipboard** functionality
- **Print optimization** for results

## 🐛 Troubleshooting

### Common Issues

**"Module not found" errors**
```bash
pip install -r requirements.txt
```

**Port already in use**
```bash
# Use a different port
uvicorn app:app --port 8001
```

**File upload fails**
- Check file format (must be .xlsx or .xls)
- Ensure file size is under 100MB
- Verify required columns exist

### Getting Help
1. Check the console for error messages
2. Verify all dependencies are installed
3. Ensure Python 3.8+ is being used
4. Try the demo mode for UI testing

## 🔒 Security Features

- File type validation
- File size limits
- Path traversal prevention
- Input sanitization
- Secure file handling

## 🚀 Performance

- **Async processing** for file uploads
- **Background tasks** for ML processing
- **Progress tracking** with real-time updates
- **Efficient memory usage** for large files
- **Caching** for repeated operations

## 📈 Future Enhancements

- [ ] Batch file processing
- [ ] Custom category training
- [ ] API authentication
- [ ] Database integration
- [ ] Advanced analytics dashboard
- [ ] Export to multiple formats

## 📄 License

This project is developed for internal use and demonstrates modern web application architecture with ML integration.

---

**Built with ❤️ using FastAPI + HTMX + TailwindCSS**