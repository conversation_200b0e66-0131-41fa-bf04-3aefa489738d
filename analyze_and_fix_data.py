"""
Analyze and fix the new_products.xlsx file
- Find product name/description columns
- Repair unit weights based on product names
- Calculate case weights
- Add PBL categories
"""

import pandas as pd
import numpy as np
from product_categorization_tool import WeightRulesProductCategorizationTool
from enhanced_categorization_tool import EnhancedWeightRulesProductCategorizationTool

def main():
    print("=== Data Analysis and Repair Tool ===")
    
    # Load the new products data
    input_file = 'new_PBL_products_0708_sziget_AMB_FF.xlsx'
    
    try:
        df = pd.read_excel(input_file)
        print(f"\n✅ Loaded {len(df)} records from {input_file}")
    except Exception as e:
        print(f"❌ Error loading file: {e}")
        return
    
    # Display basic information
    print(f"\n📋 Columns found: {list(df.columns)}")
    print(f"\n🔍 First few rows:")
    print(df.head(2).to_string())
    
    # Identify potential product name columns
    potential_name_cols = [col for col in df.columns if any(x in col.lower() for x in ['name', 'product', 'desc'])]
    print(f"\n🔍 Potential product name columns: {potential_name_cols}")
    
    print(f"\n📝 Data types:")
    print(df.dtypes)
    
    # Process products using ML-based approach
    print("\n🔧 Processing and enhancing data...")
    print("📋 Using ML-based PBL categorization with Hier_database training")
    
    # Ensure we have the right column name for product names
    if 'product_name' in df.columns:
        print("🎯 Using 'product_name' as product name column")
    else:
        print("⚠️ 'product_name' column not found - check column names")
        return
    
    # Initialize the enhanced categorization tool with zero value prediction
    tool = EnhancedWeightRulesProductCategorizationTool()
    
    # Define training parameters
    training_file_path = 'hier_database_v2.xlsb'
    pbl_column_name = 'PBL category'  # Adjust this based on actual column name in training data
    
    try:
        # Process products with zero value prediction: extract weights, predict zeros, calculate case weights, train ML model, predict PBL
        enhanced_df = tool.process_products_with_zero_prediction(df, training_file_path, pbl_column_name)
        
        print("\n📊 Processing Results:")
        
        # Display weight extraction results
        if 'extracted_unit_weight' in enhanced_df.columns:
            extracted_count = enhanced_df['extracted_unit_weight'].notna().sum()
            print(f"  Unit weights extracted from names: {extracted_count}/{len(enhanced_df)} ({extracted_count/len(enhanced_df)*100:.1f}%)")
        
        # Display corrected weights
        if 'corrected_unit_weight_g' in enhanced_df.columns:
            corrected_count = enhanced_df['corrected_unit_weight_g'].notna().sum()
            print(f"  Corrected unit weights available: {corrected_count}/{len(enhanced_df)} ({corrected_count/len(enhanced_df)*100:.1f}%)")
        
        # Display case weight calculations
        if 'calculated_case_weight' in enhanced_df.columns:
            case_weight_count = enhanced_df['calculated_case_weight'].notna().sum()
            print(f"  Case weights calculated: {case_weight_count}/{len(enhanced_df)} ({case_weight_count/len(enhanced_df)*100:.1f}%)")

        # Display zero value predictions
        if 'predicted_case_capacity' in enhanced_df.columns:
            capacity_predictions = enhanced_df['predicted_case_capacity'].sum()
            print(f"  Case capacity values predicted: {capacity_predictions}/{len(enhanced_df)} ({capacity_predictions/len(enhanced_df)*100:.1f}%)")

        if 'predicted_case_weight' in enhanced_df.columns:
            weight_predictions = enhanced_df['predicted_case_weight'].sum()
            print(f"  Case weight values predicted: {weight_predictions}/{len(enhanced_df)} ({weight_predictions/len(enhanced_df)*100:.1f}%)")
        
        # Display sample comparisons
        print(f"\n🔍 Sample weight comparisons with zero value predictions:")
        comparison_cols = ['product_name', 'original_case_capacity', 'case_capacity', 'predicted_case_capacity',
                          'original_case_weight', 'case_weight', 'predicted_case_weight', 'unit_weight',
                          'extracted_unit_weight', 'corrected_unit_weight_g', 'calculated_case_weight']
        available_cols = [col for col in comparison_cols if col in enhanced_df.columns]
        print(enhanced_df[available_cols].head(10).to_string())
        
        # Display PBL category results
        if 'predicted_pbl_category' in enhanced_df.columns:
            category_counts = enhanced_df['predicted_pbl_category'].value_counts()
            print(f"\n📊 PBL Category Distribution:")
            print(f"  Total categories assigned: {len(category_counts)}")
            print(f"\nTop 10 PBL categories:")
            print(category_counts.head(10))
        
        # Save enhanced data
        output_file = 'new_products_fixed_and_enhanced.xlsx'
        enhanced_df.to_excel(output_file, index=False)
        print(f"\n✅ Enhanced data saved to: {output_file}")
        
        # Final summary
        print(f"\n📊 PROCESSING SUMMARY:")
        print(f"Total products: {len(enhanced_df)}")
        if 'extracted_unit_weight' in enhanced_df.columns:
            print(f"Unit weights extracted from names: {enhanced_df['extracted_unit_weight'].notna().sum()}/{len(enhanced_df)} ({enhanced_df['extracted_unit_weight'].notna().sum()/len(enhanced_df)*100:.1f}%)")
        if 'corrected_unit_weight_g' in enhanced_df.columns:
            print(f"Corrected unit weights available: {enhanced_df['corrected_unit_weight_g'].notna().sum()}/{len(enhanced_df)} ({enhanced_df['corrected_unit_weight_g'].notna().sum()/len(enhanced_df)*100:.1f}%)")
        if 'calculated_case_weight' in enhanced_df.columns:
            print(f"Case weights calculated: {enhanced_df['calculated_case_weight'].notna().sum()}/{len(enhanced_df)} ({enhanced_df['calculated_case_weight'].notna().sum()/len(enhanced_df)*100:.1f}%)")
        if 'predicted_pbl_category' in enhanced_df.columns:
            print(f"PBL categories predicted: {enhanced_df['predicted_pbl_category'].notna().sum()}/{len(enhanced_df)} ({enhanced_df['predicted_pbl_category'].notna().sum()/len(enhanced_df)*100:.1f}%)")
        if 'predicted_case_capacity' in enhanced_df.columns:
            print(f"Case capacity values predicted: {enhanced_df['predicted_case_capacity'].sum()}/{len(enhanced_df)} ({enhanced_df['predicted_case_capacity'].sum()/len(enhanced_df)*100:.1f}%)")
        if 'predicted_case_weight' in enhanced_df.columns:
            print(f"Case weight values predicted: {enhanced_df['predicted_case_weight'].sum()}/{len(enhanced_df)} ({enhanced_df['predicted_case_weight'].sum()/len(enhanced_df)*100:.1f}%)")
        
    except Exception as e:
        print(f"❌ Error during processing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
