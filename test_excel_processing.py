#!/usr/bin/env python3
"""
Test script to verify the Excel processing fix works with real data.
This creates a test Excel file with mixed data types and processes it.
"""

import pandas as pd
import numpy as np
import asyncio
from enhanced_categorization_tool import EnhancedWeightRulesProductCategorizationTool

async def test_excel_processing():
    """Test processing an Excel file with mixed data types"""
    print("🧪 Testing Excel file processing with mixed data types...")
    
    # Create test data that mimics the problematic Excel file
    test_data = {
        'product_name': [
            'Test Product 1 250g',
            'Test Product 2 500ml',
            'Test Product 3 1kg',
            'Test Product 4 150g x 6',
            'Test Product 5 2.5L'
        ],
        'case_capacity': ["8", 12, "6", "0", ""],  # Mixed string and numeric
        'case_weight': ["1.2", "0", "2,5", "", "invalid"],  # Mixed with European decimals
        'unit_weight': ["0.25", 0.5, "1,0", "", "nan"],  # Mixed formats
        'DIV_DESC': ['Division A', 'Division A', 'Division B', 'Division A', 'Division B'],
        'DEP_DESC': ['Department 1', 'Department 1', 'Department 2', 'Department 1', 'Department 2'],
        'SEC_DESC': ['Section X', 'Section X', 'Section Y', 'Section X', 'Section Y'],
        'GRP_DESC': ['Group Alpha', 'Group Alpha', 'Group Beta', 'Group Alpha', 'Group Beta']
    }
    
    # Create DataFrame and save as Excel
    df = pd.DataFrame(test_data)
    test_file = 'test_mixed_types.xlsx'
    df.to_excel(test_file, index=False)
    print(f"✅ Created test Excel file: {test_file}")
    print("Test data:")
    print(df)
    print(f"Data types:\n{df.dtypes}")
    
    # Load the Excel file (this simulates how the app loads data)
    loaded_df = pd.read_excel(test_file)
    print(f"\nLoaded DataFrame data types:\n{loaded_df.dtypes}")
    
    # Initialize the tool
    tool = EnhancedWeightRulesProductCategorizationTool()
    
    # Create a simple training file for testing
    training_data = {
        'product_name': ['Training Product 1', 'Training Product 2'],
        'case_capacity': [8, 12],
        'case_weight': [2.0, 3.0],
        'unit_weight': [0.25, 0.25],
        'DIV_DESC': ['Division A', 'Division B'],
        'DEP_DESC': ['Department 1', 'Department 2'],
        'SEC_DESC': ['Section X', 'Section Y'],
        'GRP_DESC': ['Group Alpha', 'Group Beta'],
        'PBL category': ['Category 1', 'Category 2']
    }
    training_df = pd.DataFrame(training_data)
    training_file = 'test_training.xlsx'
    training_df.to_excel(training_file, index=False)
    
    print(f"\n🔄 Processing data with the enhanced tool...")
    
    try:
        # This should not raise the type comparison error anymore
        processed_df = await tool.process_products_with_zero_prediction(
            loaded_df, 
            training_file, 
            'PBL category'
        )
        
        print("✅ Processing completed successfully!")
        print("\nProcessed data sample:")
        print(processed_df[['product_name', 'case_capacity', 'case_weight', 'unit_weight']].head())
        
        # Check that numeric columns are properly converted
        print(f"\nProcessed data types:\n{processed_df.dtypes}")
        
        # Verify that the problematic comparisons work
        capacity_zeros = (processed_df['case_capacity'] == 0).sum()
        weight_zeros = (processed_df['case_weight'] == 0).sum()
        print(f"\nZero values found:")
        print(f"  Case capacity zeros: {capacity_zeros}")
        print(f"  Case weight zeros: {weight_zeros}")
        
        return True
        
    except Exception as e:
        print(f"❌ Processing failed: {e}")
        if "'>' not supported between instances of 'str' and 'int'" in str(e):
            print("The original type comparison error still occurs!")
        return False

async def main():
    """Run the Excel processing test"""
    print("🚀 Starting Excel processing test...\n")
    
    try:
        success = await test_excel_processing()
        
        if success:
            print("\n🎉 Excel processing test passed!")
            print("The application can now handle Excel files with mixed data types.")
        else:
            print("\n❌ Excel processing test failed!")
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
