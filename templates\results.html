<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Processing Results - WLM Category Classifier</title>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <header class="text-center mb-8">
            <div class="inline-block p-4 bg-green-100 rounded-full shadow-lg mb-4">
                <i class="fas fa-check-circle text-3xl text-green-600"></i>
            </div>
            <h1 class="text-4xl font-bold text-gray-800 mb-2">Processing Complete!</h1>
            <p class="text-lg text-gray-600">Your data has been successfully analyzed and enhanced</p>
        </header>

        <!-- Summary Card -->
        <div class="max-w-6xl mx-auto mb-8">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <div class="flex flex-col md:flex-row items-center justify-between mb-6">
                    <div>
                        <h2 class="text-2xl font-semibold text-gray-800 mb-2">
                            <i class="fas fa-file-excel text-blue-600 mr-2"></i>
                            {{ task.filename }}
                        </h2>
                        <p class="text-gray-600">
                            Processed {{ task.stats.total_products }} products with advanced ML categorization
                        </p>
                    </div>
                    <div class="mt-4 md:mt-0 space-y-2 md:space-y-0 md:space-x-4 flex flex-col md:flex-row">
                        <a href="/download/{{ task.task_id }}" 
                           class="bg-gradient-to-r from-green-600 to-emerald-600 text-white py-3 px-6 rounded-xl font-semibold hover:from-green-700 hover:to-emerald-700 transform hover:scale-105 transition-all duration-200 shadow-lg text-center">
                            <i class="fas fa-download mr-2"></i>
                            Download Results
                        </a>
                        <a href="/" 
                           class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-3 px-6 rounded-xl font-semibold hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-200 shadow-lg text-center">
                            <i class="fas fa-plus mr-2"></i>
                            Process Another File
                        </a>
                    </div>
                </div>

                <!-- Processing Time -->
                <div class="text-sm text-gray-500 mb-6">
                    <i class="fas fa-clock mr-1"></i>
                    Started at: {{ task.started_at.strftime('%Y-%m-%d %H:%M:%S') }}
                </div>

                <!-- Statistics Grid -->
                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- Total Products -->
                    <div class="bg-blue-50 rounded-xl p-6 text-center">
                        <div class="text-3xl font-bold text-blue-600 mb-2">{{ task.stats.total_products }}</div>
                        <div class="text-sm font-medium text-blue-700">Total Products</div>
                    </div>

                    <!-- Weight Extraction -->
                    {% if task.stats.unit_weights_extracted %}
                    <div class="bg-green-50 rounded-xl p-6 text-center">
                        <div class="text-3xl font-bold text-green-600 mb-2">{{ task.stats.unit_weights_extracted.percentage }}%</div>
                        <div class="text-sm font-medium text-green-700">Unit Weights Extracted</div>
                        <div class="text-xs text-gray-600 mt-1">{{ task.stats.unit_weights_extracted.count }} products</div>
                    </div>
                    {% endif %}

                    <!-- Case Weights -->
                    {% if task.stats.case_weights_calculated %}
                    <div class="bg-purple-50 rounded-xl p-6 text-center">
                        <div class="text-3xl font-bold text-purple-600 mb-2">{{ task.stats.case_weights_calculated.percentage }}%</div>
                        <div class="text-sm font-medium text-purple-700">Case Weights Calculated</div>
                        <div class="text-xs text-gray-600 mt-1">{{ task.stats.case_weights_calculated.count }} products</div>
                    </div>
                    {% endif %}

                    <!-- PBL Categories -->
                    {% if task.stats.pbl_categories %}
                    <div class="bg-orange-50 rounded-xl p-6 text-center">
                        <div class="text-3xl font-bold text-orange-600 mb-2">{{ task.stats.pbl_categories.unique_categories }}</div>
                        <div class="text-sm font-medium text-orange-700">PBL Categories</div>
                        <div class="text-xs text-gray-600 mt-1">{{ task.stats.pbl_categories.percentage }}% assigned</div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Detailed Statistics -->
        <div class="max-w-6xl mx-auto grid lg:grid-cols-2 gap-8">
            <!-- Processing Details -->
            <div class="bg-white rounded-2xl shadow-xl p-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">
                    <i class="fas fa-cog text-blue-600 mr-2"></i>
                    Processing Details
                </h3>
                
                <div class="space-y-4">
                    {% if task.stats.unit_weights_extracted %}
                    <div class="flex items-center justify-between py-4 px-5 bg-green-50 border border-green-200 rounded-lg hover:shadow-md transition-shadow">
                        <div class="flex items-center">
                            <i class="fas fa-weight-hanging text-green-600 mr-3 text-lg"></i>
                            <span class="font-semibold text-gray-800">Unit Weights Extracted</span>
                        </div>
                        <div class="text-right">
                            <div class="font-bold text-green-700 text-lg">{{ task.stats.unit_weights_extracted.count }}</div>
                            <div class="text-sm font-medium text-green-600">{{ task.stats.unit_weights_extracted.percentage }}%</div>
                        </div>
                    </div>
                    {% endif %}

                    {% if task.stats.unit_weights_corrected %}
                    <div class="flex items-center justify-between py-4 px-5 bg-blue-50 border border-blue-200 rounded-lg hover:shadow-md transition-shadow">
                        <div class="flex items-center">
                            <i class="fas fa-edit text-blue-600 mr-3 text-lg"></i>
                            <span class="font-semibold text-gray-800">Unit Weights Corrected</span>
                        </div>
                        <div class="text-right">
                            <div class="font-bold text-blue-700 text-lg">{{ task.stats.unit_weights_corrected.count }}</div>
                            <div class="text-sm font-medium text-blue-600">{{ task.stats.unit_weights_corrected.percentage }}%</div>
                        </div>
                    </div>
                    {% endif %}

                    {% if task.stats.case_capacity_predictions %}
                    <div class="flex items-center justify-between py-4 px-5 bg-purple-50 border border-purple-200 rounded-lg hover:shadow-md transition-shadow">
                        <div class="flex items-center">
                            <i class="fas fa-brain text-purple-600 mr-3 text-lg"></i>
                            <span class="font-semibold text-gray-800">Case Capacity Predictions</span>
                        </div>
                        <div class="text-right">
                            <div class="font-bold text-purple-700 text-lg">{{ task.stats.case_capacity_predictions.count }}</div>
                            <div class="text-sm font-medium text-purple-600">{{ task.stats.case_capacity_predictions.percentage }}%</div>
                        </div>
                    </div>
                    {% endif %}

                    {% if task.stats.case_weight_predictions %}
                    <div class="flex items-center justify-between py-4 px-5 bg-orange-50 border border-orange-200 rounded-lg hover:shadow-md transition-shadow">
                        <div class="flex items-center">
                            <i class="fas fa-calculator text-orange-600 mr-3 text-lg"></i>
                            <span class="font-semibold text-gray-800">Case Weight Predictions</span>
                        </div>
                        <div class="text-right">
                            <div class="font-bold text-orange-700 text-lg">{{ task.stats.case_weight_predictions.count }}</div>
                            <div class="text-sm font-medium text-orange-600">{{ task.stats.case_weight_predictions.percentage }}%</div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- PBL Categories Chart -->
            {% if task.stats.pbl_categories and task.stats.pbl_categories.top_categories %}
            <div class="bg-white rounded-2xl shadow-xl p-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">
                    <i class="fas fa-chart-pie text-orange-600 mr-2"></i>
                    Top PBL Categories
                </h3>
                
                <div class="relative h-64 mb-4">
                    <canvas id="categoriesChart"></canvas>
                </div>

                <!-- Category List -->
                <div class="max-h-32 overflow-y-auto space-y-2">
                    {% for category, count in task.stats.pbl_categories.top_categories.items() %}
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-700 truncate">{{ category }}</span>
                        <span class="font-semibold text-gray-900 ml-2">{{ count }}</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Data Preview Section -->
        <div class="max-w-6xl mx-auto mt-8">
            <div class="bg-white rounded-2xl shadow-xl p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-semibold text-gray-800">
                        <i class="fas fa-table text-indigo-600 mr-2"></i>
                        Processed Data Preview
                    </h3>
                    <button onclick="toggleDataPreview()" 
                            class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-2 px-4 rounded-lg font-semibold hover:from-indigo-700 hover:to-purple-700 transform hover:scale-105 transition-all duration-200 shadow-lg">
                        <i class="fas fa-eye mr-2"></i>
                        <span id="preview-btn-text">Show Results</span>
                    </button>
                </div>
                
                <div id="data-preview" class="hidden">
                    <p class="text-gray-600 mb-4">Sample of your processed data with enhanced columns:</p>
                    
                    <div class="overflow-x-auto bg-gray-50 rounded-lg border">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Product Name</th>
                                    <th class="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Original Weight (kilograms)</th>
                                    <th class="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Extracted Weight (grams)</th>
                                    <th class="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Case Capacity</th>
                                    <th class="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">PBL Category</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% if task.sample_data %}
                                    {% for row in task.sample_data %}
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-4 py-3 text-sm font-medium text-gray-900">{{ row.product_name }}</td>
                                        <td class="px-4 py-3 text-sm text-gray-600">{{ row.original_weight }}</td>
                                        <td class="px-4 py-3 text-sm text-green-600 font-semibold">{{ row.extracted_weight }}</td>
                                        <td class="px-4 py-3 text-sm text-blue-600 font-semibold">{{ row.case_capacity }}</td>
                                        <td class="px-4 py-3 text-sm text-purple-600 font-semibold">{{ row.pbl_category }}</td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr class="hover:bg-gray-50">
                                        <td colspan="5" class="px-4 py-3 text-sm text-gray-600 text-center">
                                            <i class="fas fa-info-circle mr-2"></i>
                                            Sample data will be available after processing is complete
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <h4 class="font-semibold text-blue-800 mb-2">
                            <i class="fas fa-info-circle mr-2"></i>
                            Enhanced Columns Added:
                        </h4>
                        <div class="grid md:grid-cols-2 gap-2 text-sm">
                            <div class="flex items-center text-green-700">
                                <i class="fas fa-search mr-2"></i>
                                <span>Extracted unit weights from names</span>
                            </div>
                            <div class="flex items-center text-blue-700">
                                <i class="fas fa-calculator mr-2"></i>
                                <span>Calculated case weights</span>
                            </div>
                            <div class="flex items-center text-purple-700">
                                <i class="fas fa-brain mr-2"></i>
                                <span>ML-predicted missing values</span>
                            </div>
                            <div class="flex items-center text-orange-700">
                                <i class="fas fa-tags mr-2"></i>
                                <span>Smart PBL categorization</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Toggle data preview
        function toggleDataPreview() {
            const preview = document.getElementById('data-preview');
            const btnText = document.getElementById('preview-btn-text');
            const btn = btnText.parentElement;
            
            if (preview.classList.contains('hidden')) {
                preview.classList.remove('hidden');
                preview.classList.add('animate-fade-in');
                btnText.textContent = 'Hide Results';
                btn.querySelector('i').className = 'fas fa-eye-slash mr-2';
                btn.classList.add('from-red-600', 'to-pink-600', 'hover:from-red-700', 'hover:to-pink-700');
                btn.classList.remove('from-indigo-600', 'to-purple-600', 'hover:from-indigo-700', 'hover:to-purple-700');
            } else {
                preview.classList.add('hidden');
                preview.classList.remove('animate-fade-in');
                btnText.textContent = 'Show Results';
                btn.querySelector('i').className = 'fas fa-eye mr-2';
                btn.classList.remove('from-red-600', 'to-pink-600', 'hover:from-red-700', 'hover:to-pink-700');
                btn.classList.add('from-indigo-600', 'to-purple-600', 'hover:from-indigo-700', 'hover:to-purple-700');
            }
        }

        // Chart for PBL categories
        {% if task.stats.pbl_categories and task.stats.pbl_categories.top_categories %}
        const ctx = document.getElementById('categoriesChart').getContext('2d');
        const data = {{ task.stats.pbl_categories.top_categories | tojson }};
        
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: Object.keys(data).slice(0, 8),
                datasets: [{
                    data: Object.values(data).slice(0, 8),
                    backgroundColor: [
                        '#3B82F6', '#10B981', '#F59E0B', '#EF4444',
                        '#8B5CF6', '#06B6D4', '#F97316', '#84CC16'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        },
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#ffffff',
                        borderWidth: 1
                    }
                },
                elements: {
                    arc: {
                        borderWidth: 2
                    }
                }
            }
        });
        {% endif %}
    </script>
</body>
</html>