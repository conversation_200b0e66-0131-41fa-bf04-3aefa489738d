#!/usr/bin/env python3
"""
Focused test to verify the type comparison fix works correctly.
This test specifically targets the "'>' not supported between instances of 'str' and 'int'" error.
"""

import pandas as pd
import numpy as np
import asyncio
from enhanced_categorization_tool import <PERSON><PERSON><PERSON>uePredictor, EnhancedWeightRulesProductCategorizationTool

def test_original_error_scenario():
    """Test the exact scenario that caused the original error"""
    print("🧪 Testing the original error scenario...")
    
    # Create data that would cause the original error
    # This simulates data loaded from Excel with string values in numeric columns
    problematic_data = {
        'case_capacity': ["8", "12", "0", "", "6.5", "nan", "invalid"],
        'case_weight': ["1.2", "0", "2,5", "", "3.0", "nan", "invalid"],
        'DIV_DESC': ['Div1', 'Div1', 'Div2', 'Div1', 'Div2', 'Div1', 'Div2'],
        'DEP_DESC': ['Dep1', 'Dep1', 'Dep2', 'Dep1', 'Dep2', 'Dep1', 'Dep2'],
        'SEC_DESC': ['Sec1', 'Sec1', 'Sec2', 'Sec1', 'Sec2', 'Sec1', 'Sec2'],
        'GRP_DESC': ['Grp1', 'Grp1', 'Grp2', 'Grp1', 'Grp2', 'Grp1', 'Grp2']
    }
    
    df = pd.DataFrame(problematic_data)
    print("Problematic DataFrame (simulating Excel data with string values):")
    print(df)
    print(f"Data types:\n{df.dtypes}")
    
    # Test the ZeroValuePredictor with this data
    predictor = ZeroValuePredictor()
    
    print("\n🔄 Testing zero value predictor training...")
    try:
        # This would have failed with the original code
        predictor._calculate_case_capacity_stats(df)
        predictor._calculate_case_weight_stats(df)
        print("✅ Zero value predictor training completed without type comparison errors!")
        
        # Test the mask creation
        capacity_mask = predictor._create_numeric_mask(df, 'case_capacity', 0, '>')
        weight_mask = predictor._create_numeric_mask(df, 'case_weight', 0, '>')
        zero_capacity_mask = predictor._create_numeric_mask(df, 'case_capacity', 0, '==')
        zero_weight_mask = predictor._create_numeric_mask(df, 'case_weight', 0, '==')
        
        print(f"case_capacity > 0: {capacity_mask.sum()} records")
        print(f"case_weight > 0: {weight_mask.sum()} records")
        print(f"case_capacity == 0: {zero_capacity_mask.sum()} records")
        print(f"case_weight == 0: {zero_weight_mask.sum()} records")
        
        return True
        
    except TypeError as e:
        if "'>' not supported between instances of 'str' and 'int'" in str(e):
            print(f"❌ Original type comparison error still occurs: {e}")
            return False
        else:
            print(f"❌ Unexpected error: {e}")
            return False

async def test_predict_zero_values():
    """Test the predict_zero_values method with mixed data types"""
    print("\n🧪 Testing predict_zero_values with mixed data types...")
    
    # Create test data
    test_data = {
        'product_name': ['Product A', 'Product B', 'Product C', 'Product D'],
        'case_capacity': ["0", "8", "", "12"],  # Mixed with zeros and strings
        'case_weight': ["0", "1.2", "invalid", "2,5"],  # Mixed with zeros and invalid values
        'unit_weight': ["0.25", "0", "0.5", ""],
        'DIV_DESC': ['Div1', 'Div1', 'Div2', 'Div1'],
        'DEP_DESC': ['Dep1', 'Dep1', 'Dep2', 'Dep1'],
        'SEC_DESC': ['Sec1', 'Sec1', 'Sec2', 'Sec1'],
        'GRP_DESC': ['Grp1', 'Grp1', 'Grp2', 'Grp1']
    }
    
    df = pd.DataFrame(test_data)
    print("Test DataFrame:")
    print(df)
    
    # Create training data
    training_data = {
        'case_capacity': [8, 12, 6],
        'case_weight': [2.0, 3.0, 1.5],
        'DIV_DESC': ['Div1', 'Div1', 'Div2'],
        'DEP_DESC': ['Dep1', 'Dep1', 'Dep2'],
        'SEC_DESC': ['Sec1', 'Sec1', 'Sec2'],
        'GRP_DESC': ['Grp1', 'Grp1', 'Grp2']
    }
    training_df = pd.DataFrame(training_data)
    
    # Initialize and train predictor
    tool = EnhancedWeightRulesProductCategorizationTool()
    
    # Preprocess the data
    df = tool._preprocess_numeric_columns(df)
    training_df = tool._preprocess_numeric_columns(training_df)
    
    # Train the predictor
    tool.zero_predictor.train_from_data(training_df, df)
    
    try:
        # This should work without type comparison errors
        result_df = await tool.predict_zero_values(df)
        print("✅ predict_zero_values completed successfully!")
        
        print("\nResults:")
        print(result_df[['product_name', 'case_capacity', 'case_weight', 'predicted_case_capacity', 'predicted_case_weight']])
        
        return True
        
    except TypeError as e:
        if "'>' not supported between instances of 'str' and 'int'" in str(e):
            print(f"❌ Type comparison error in predict_zero_values: {e}")
            return False
        else:
            print(f"❌ Unexpected error: {e}")
            return False

def test_data_preprocessing():
    """Test that data preprocessing correctly handles mixed types"""
    print("\n🧪 Testing data preprocessing with Excel-like mixed types...")
    
    # Simulate data as it would be loaded from Excel
    excel_like_data = {
        'product_name': ['Product 1', 'Product 2', 'Product 3'],
        'case_capacity': ["8", 12.0, "6.5"],  # Mixed string and numeric
        'case_weight': ["1.2", "2,5", 3],     # European decimal format
        'unit_weight': ["0.15", 0.25, "0,3"], # Mixed formats
        'other_column': ['A', 'B', 'C']        # Non-numeric should be unchanged
    }
    
    df = pd.DataFrame(excel_like_data)
    print("Original DataFrame (Excel-like mixed types):")
    print(df)
    print(f"Original data types:\n{df.dtypes}")
    
    # Test preprocessing
    tool = EnhancedWeightRulesProductCategorizationTool()
    processed_df = tool._preprocess_numeric_columns(df)
    
    print("\nProcessed DataFrame:")
    print(processed_df)
    print(f"Processed data types:\n{processed_df.dtypes}")
    
    # Verify numeric columns are properly converted
    expected_capacity = [8.0, 12.0, 6.5]
    expected_weight = [1.2, 2.5, 3.0]
    expected_unit_weight = [0.15, 0.25, 0.3]
    
    assert processed_df['case_capacity'].tolist() == expected_capacity
    assert processed_df['case_weight'].tolist() == expected_weight
    assert processed_df['unit_weight'].tolist() == expected_unit_weight
    assert processed_df['other_column'].tolist() == ['A', 'B', 'C']
    
    print("✅ Data preprocessing correctly handled mixed types!")
    return True

async def main():
    """Run all focused tests"""
    print("🚀 Starting focused type comparison fix tests...\n")
    
    try:
        # Test 1: Original error scenario
        test1_passed = test_original_error_scenario()
        
        # Test 2: Data preprocessing
        test2_passed = test_data_preprocessing()
        
        # Test 3: Predict zero values
        test3_passed = await test_predict_zero_values()
        
        if test1_passed and test2_passed and test3_passed:
            print("\n🎉 All focused tests passed!")
            print("✅ The type comparison error has been successfully fixed!")
            print("✅ The application can now handle Excel files with string values in numeric columns.")
            print("✅ The 'Training zero value predictor' phase will no longer fail with type comparison errors.")
        else:
            print("\n❌ Some tests failed!")
            print("The type comparison fix may not be complete.")
            
    except Exception as e:
        print(f"\n❌ Tests failed with error: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
