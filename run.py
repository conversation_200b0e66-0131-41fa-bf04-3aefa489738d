#!/usr/bin/env python3
"""
WLM Category Classifier - Startup Script
This script provides multiple ways to run the application
"""

import sys
import os
import subprocess

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'fastapi', 'uvicorn', 'pandas', 'numpy', 
        'scikit-learn', 'openpyxl', 'jinja2'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies():
    """Install dependencies using pip"""
    print("📦 Installing required dependencies...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies automatically.")
        print("💡 Please install manually using: pip install -r requirements.txt")
        return False

def run_full_app():
    """Run the full FastAPI application"""
    try:
        import uvicorn
        print("🚀 Starting WLM Category Classifier (Full Version)")
        print("📊 Access the app at: http://localhost:8000")
        print("⚡ Press Ctrl+C to stop")
        uvicorn.run("app:app", host="0.0.0.0", port=8000, reload=True)
    except ImportError:
        print("❌ FastAPI/Uvicorn not found. Running demo instead...")
        run_demo_app()

def run_demo_app():
    """Run the demo version without ML dependencies"""
    print("🎭 Starting Demo Version (No ML dependencies required)")
    print("📊 Access the app at: http://localhost:8000")
    print("💡 This version shows the UI and basic functionality")
    print("⚡ Press Ctrl+C to stop")
    
    try:
        exec(open('app_demo.py').read())
    except KeyboardInterrupt:
        print("\n🛑 Demo server stopped")
    except Exception as e:
        print(f"❌ Error running demo: {e}")

def main():
    """Main startup function"""
    print("=" * 60)
    print("🏷️  WLM CATEGORY CLASSIFIER")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not os.path.exists('app.py'):
        print("❌ Please run this script from the wlm_category_classifier directory")
        sys.exit(1)
    
    # Check dependencies
    missing = check_dependencies()
    
    if not missing:
        print("✅ All dependencies found!")
        run_full_app()
    else:
        print(f"⚠️  Missing dependencies: {', '.join(missing)}")
        
        choice = input("\nChoose an option:\n"
                      "1. Install dependencies automatically\n"
                      "2. Run demo version (no ML features)\n"
                      "3. Exit\n"
                      "Enter your choice (1-3): ").strip()
        
        if choice == '1':
            if install_dependencies():
                print("\n🔄 Restarting with full functionality...")
                run_full_app()
            else:
                print("\n🎭 Falling back to demo version...")
                run_demo_app()
        elif choice == '2':
            run_demo_app()
        else:
            print("👋 Goodbye!")
            sys.exit(0)

if __name__ == "__main__":
    main()