"""
WLM Category Classifier Demo Application
Simplified version for demonstration without ML dependencies
"""

import os
import uuid
import json
from datetime import datetime
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
import cgi
import urllib.parse

# Demo data for showcasing functionality
DEMO_STATS = {
    "total_products": 1234,
    "unit_weights_extracted": {"count": 987, "percentage": 80.0},
    "unit_weights_corrected": {"count": 856, "percentage": 69.4},
    "case_weights_calculated": {"count": 1100, "percentage": 89.2},
    "case_capacity_predictions": {"count": 234, "percentage": 19.0},
    "case_weight_predictions": {"count": 178, "percentage": 14.4},
    "pbl_categories": {
        "total_assigned": 1200,
        "percentage": 97.2,
        "unique_categories": 45,
        "top_categories": {
            "Fresh Produce": 234,
            "Dairy & Refrigerated": 189,
            "Bakery": 156,
            "Meat & Poultry": 134,
            "Frozen Foods": 112,
            "Beverages": 98,
            "Pantry Staples": 87,
            "Snacks & Confectionery": 76
        }
    },
    "columns_processed": [
        "product_name", "case_capacity", "case_weight", "unit_weight",
        "extracted_unit_weight", "corrected_unit_weight_g", "calculated_case_weight",
        "predicted_case_capacity", "predicted_case_weight", "predicted_pbl_category",
        "DIV_DESC", "DEP_DESC", "SEC_DESC", "GRP_DESC"
    ]
}

class DemoHandler(SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory="/home/<USER>/wlm_category_classifier", **kwargs)
    
    def do_GET(self):
        if self.path == '/':
            self.serve_template('templates/index.html')
        elif self.path.startswith('/results/'):
            task_id = self.path.split('/')[-1]
            self.serve_results_page(task_id)
        elif self.path.startswith('/status/'):
            task_id = self.path.split('/')[-1]
            self.serve_status_json(task_id)
        elif self.path.startswith('/download/'):
            task_id = self.path.split('/')[-1]
            self.serve_download(task_id)
        elif self.path.startswith('/static/'):
            # Serve static files
            super().do_GET()
        else:
            self.send_error(404)
    
    def do_POST(self):
        if self.path == '/upload':
            self.handle_upload()
        else:
            self.send_error(404)
    
    def serve_template(self, template_path):
        try:
            with open(template_path, 'r') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(content.encode())
        except FileNotFoundError:
            self.send_error(404)
    
    def serve_results_page(self, task_id):
        try:
            # Create a mock task object
            mock_task = type('Task', (), {
                'task_id': task_id,
                'filename': 'demo_products.xlsx',
                'status': 'completed',
                'stats': DEMO_STATS,
                'started_at': datetime.now()
            })()
            
            with open('templates/results.html', 'r') as f:
                content = f.read()
            
            # Simple template replacement (not full Jinja2, just demo)
            content = content.replace('{{ task.task_id }}', task_id)
            content = content.replace('{{ task.filename }}', 'demo_products.xlsx')
            content = content.replace('{{ task.stats.total_products }}', str(DEMO_STATS['total_products']))
            
            # Replace percentage values
            for key, value in DEMO_STATS.items():
                if isinstance(value, dict) and 'percentage' in value:
                    content = content.replace(f'{{{{ task.stats.{key}.percentage }}}}', str(value['percentage']))
                    content = content.replace(f'{{{{ task.stats.{key}.count }}}}', str(value['count']))
            
            # Handle PBL categories
            if 'pbl_categories' in DEMO_STATS:
                pbl = DEMO_STATS['pbl_categories']
                content = content.replace('{{ task.stats.pbl_categories.unique_categories }}', str(pbl['unique_categories']))
                content = content.replace('{{ task.stats.pbl_categories.percentage }}', str(pbl['percentage']))
                
                # Generate category list
                category_html = ""
                for category, count in pbl['top_categories'].items():
                    category_html += f'''
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-700 truncate">{category}</span>
                        <span class="font-semibold text-gray-900 ml-2">{count}</span>
                    </div>
                    '''
                
                # Replace the category loop (simplified)
                loop_start = content.find('{% for category, count in task.stats.pbl_categories.top_categories.items() %}')
                loop_end = content.find('{% endfor %}', loop_start)
                if loop_start != -1 and loop_end != -1:
                    content = content[:loop_start] + category_html + content[loop_end + len('{% endfor %}'):]
            
            # Handle columns list
            columns_html = ""
            for column in DEMO_STATS['columns_processed']:
                icon_class = "fas fa-columns text-gray-600"
                if 'extracted' in column:
                    icon_class = "fas fa-search text-green-600"
                elif 'predicted' in column:
                    icon_class = "fas fa-brain text-purple-600"
                elif 'corrected' in column:
                    icon_class = "fas fa-edit text-blue-600"
                elif 'calculated' in column:
                    icon_class = "fas fa-calculator text-orange-600"
                
                columns_html += f'''
                <div class="flex items-center py-2 px-3 bg-gray-50 rounded-lg">
                    <i class="{icon_class} mr-2"></i>
                    <span class="text-sm font-medium text-gray-700">{column}</span>
                </div>
                '''
            
            # Replace columns loop
            loop_start = content.find('{% for column in task.stats.columns_processed %}')
            loop_end = content.find('{% endfor %}', loop_start)
            if loop_start != -1 and loop_end != -1:
                content = content[:loop_start] + columns_html + content[loop_end + len('{% endfor %}'):]
            
            # Clean up remaining Jinja2 syntax
            content = content.replace('{{ task.started_at.strftime(\'%Y-%m-%d %H:%M:%S\') }}', 
                                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            # Remove conditional blocks for demo
            content = self.remove_jinja_blocks(content)
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(content.encode())
            
        except Exception as e:
            print(f"Error serving results: {e}")
            self.send_error(500)
    
    def remove_jinja_blocks(self, content):
        """Remove Jinja2 conditional blocks for demo"""
        import re
        
        # Remove {% if ... %} and {% endif %} blocks, keeping content
        content = re.sub(r'\{%\s*if\s+[^%]+%\}', '', content)
        content = re.sub(r'\{%\s*endif\s*%\}', '', content)
        
        # Remove any remaining Jinja2 syntax
        content = re.sub(r'\{\{[^}]+\}\}', '', content)
        content = re.sub(r'\{%[^%]+%\}', '', content)
        
        return content
    
    def serve_status_json(self, task_id):
        status_data = {
            "task_id": task_id,
            "status": "completed",
            "progress": 100,
            "message": "Processing completed successfully!",
            "result_file": f"{task_id}_processed.xlsx",
            "error": None,
            "stats": DEMO_STATS,
            "started_at": datetime.now().isoformat()
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(status_data).encode())
    
    def serve_download(self, task_id):
        # Create a simple demo file
        demo_content = "This is a demo processed file for task: " + task_id
        
        self.send_response(200)
        self.send_header('Content-type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        self.send_header('Content-Disposition', f'attachment; filename="processed_demo.xlsx"')
        self.end_headers()
        self.wfile.write(demo_content.encode())
    
    def handle_upload(self):
        # Parse the multipart form data
        task_id = str(uuid.uuid4())
        
        response_data = {
            "task_id": task_id,
            "filename": "uploaded_file.xlsx",
            "status": "uploaded"
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response_data).encode())

def run_demo_server():
    server_address = ('', 8000)
    httpd = HTTPServer(server_address, DemoHandler)
    print("🚀 Demo WLM Category Classifier running at http://localhost:8000")
    print("📝 This is a demo version showcasing the UI and basic functionality")
    print("💡 To run the full ML-powered version, install dependencies: pip install -r requirements.txt")
    print("⚡ Press Ctrl+C to stop the server")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
        httpd.server_close()

if __name__ == "__main__":
    run_demo_server()